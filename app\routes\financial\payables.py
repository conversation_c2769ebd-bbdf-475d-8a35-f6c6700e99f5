"""
应付账款管理路由
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app import db, csrf
from app.routes.financial import financial_bp
from app.models_financial import AccountPayable, FinancialVoucher, VoucherDetail, AccountingSubject
from app.models import StockIn, Supplier
from app.utils.school_required import school_required
from app.utils.permissions import check_permission
from sqlalchemy import text
from datetime import datetime, date


@financial_bp.route('/payables')
@login_required
@school_required
@check_permission('应付账款管理', 'view')
def payables_index(user_area):
    """应付账款列表"""

    # 添加调试信息
    current_app.logger.info(f"用户 {current_user.username} 访问应付账款页面")
    current_app.logger.info(f"用户区域: {user_area.name if user_area else 'None'}")
    current_app.logger.info(f"用户权限: {current_user.has_permission('应付账款管理', 'view')}")

    # 获取搜索参数
    keyword = request.args.get('keyword', '').strip()
    supplier_id = request.args.get('supplier_id', type=int)
    status = request.args.get('status', '').strip()
    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()
    
    # 构建查询
    query = AccountPayable.query.filter_by(area_id=user_area.id)
    
    if keyword:
        query = query.filter(
            db.or_(
                AccountPayable.payable_number.like(f'%{keyword}%'),
                AccountPayable.invoice_number.like(f'%{keyword}%')
            )
        )
    
    if supplier_id:
        query = query.filter_by(supplier_id=supplier_id)
    
    if status:
        query = query.filter_by(status=status)
    
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(AccountPayable.created_at >= start_date_obj)
        except ValueError:
            pass
    
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(AccountPayable.created_at <= end_date_obj)
        except ValueError:
            pass
    
    # 分页
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ITEMS_PER_PAGE', 20)
    
    payables = query.order_by(AccountPayable.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # 获取供应商列表用于筛选
    supplier_ids = db.session.query(AccountPayable.supplier_id).filter_by(area_id=user_area.id).distinct().all()
    supplier_ids = [sid[0] for sid in supplier_ids if sid[0] is not None]

    if supplier_ids:
        suppliers = Supplier.query.filter(Supplier.id.in_(supplier_ids)).all()
    else:
        suppliers = []
    
    return render_template('financial/payables/index.html',
                         payables=payables,
                         suppliers=suppliers,
                         keyword=keyword,
                         supplier_id=supplier_id,
                         status=status,
                         start_date=start_date,
                         end_date=end_date)


@financial_bp.route('/payables/<int:id>')
@login_required
@school_required
@check_permission('应付账款管理', 'view')
def view_payable(id):
    """查看应付账款详情"""
    user_area = current_user.get_current_area()
    payable = AccountPayable.query.filter_by(
        id=id, 
        area_id=user_area.id
    ).first_or_404()
    
    # 获取付款记录
    from app.models_financial import PaymentRecord
    payments = PaymentRecord.query.filter_by(payable_id=id).order_by(PaymentRecord.payment_date.desc()).all()
    
    return render_template('financial/payables/view.html', 
                         payable=payable, payments=payments)


@financial_bp.route('/payables/generate-from-stock-in', methods=['POST'])
@csrf.exempt
@login_required
@school_required
@check_permission('应付账款管理', 'create')
def generate_payable_from_stock_in(user_area):
    """从入库单生成应付账款"""
    current_app.logger.info("=== 开始生成应付账款 ===")

    # 获取请求数据
    request_data = request.get_json()
    current_app.logger.info(f"请求数据: {request_data}")

    stock_in_id = request_data.get('stock_in_id') if request_data else None
    current_app.logger.info(f"入库单ID: {stock_in_id}")

    if not stock_in_id:
        current_app.logger.error("未提供入库单ID")
        return jsonify({'success': False, 'message': '请选择入库单'})
    
    # 检查入库单是否存在且属于当前学校
    current_app.logger.info(f"查询入库单: ID={stock_in_id}, area_id={user_area.id}")
    stock_in = StockIn.query.filter_by(
        id=stock_in_id,
        area_id=user_area.id
    ).first()

    if not stock_in:
        current_app.logger.error(f"入库单不存在: ID={stock_in_id}")
        return jsonify({'success': False, 'message': '入库单不存在'})

    current_app.logger.info(f"找到入库单: {stock_in.stock_in_number}, 状态: {stock_in.status}, 总金额: {stock_in.total_cost}")
    
    # 检查是否已生成应付账款
    current_app.logger.info("检查是否已生成应付账款...")
    existing = AccountPayable.query.filter_by(stock_in_id=stock_in_id).first()
    if existing:
        current_app.logger.error(f"该入库单已生成应付账款: {existing.payable_number}")
        return jsonify({'success': False, 'message': '该入库单已生成应付账款'})

    # 检查入库单是否已财务确认
    current_app.logger.info(f"检查入库单状态: {stock_in.status}")
    if stock_in.status != '已入库':
        current_app.logger.error(f"入库单状态不正确: {stock_in.status}")
        return jsonify({'success': False, 'message': '入库单尚未入库'})
    
    try:
        current_app.logger.info("开始生成应付账款...")

        # 生成应付账款编号
        today = date.today()
        prefix = f"AP{today.strftime('%Y%m%d')}"
        current_app.logger.info(f"应付账款编号前缀: {prefix}")

        last_payable = AccountPayable.query.filter(
            AccountPayable.area_id == user_area.id,
            AccountPayable.payable_number.like(f'{prefix}%')
        ).order_by(AccountPayable.payable_number.desc()).first()

        if last_payable:
            last_number = int(last_payable.payable_number[-3:])
            payable_number = f"{prefix}{last_number + 1:03d}"
            current_app.logger.info(f"基于最后编号 {last_payable.payable_number} 生成新编号: {payable_number}")
        else:
            payable_number = f"{prefix}001"
            current_app.logger.info(f"生成首个应付账款编号: {payable_number}")
        
        # 使用原始 SQL 创建应付账款（遵循 README.md 方案：不包含时间字段）
        sql_payable = text("""
            INSERT INTO account_payables
            (payable_number, area_id, supplier_id, stock_in_id, purchase_order_id,
             original_amount, paid_amount, balance_amount, due_date, status,
             payment_terms, invoice_number, invoice_date, invoice_amount,
             created_by, notes)
            OUTPUT inserted.id
            VALUES
            (:payable_number, :area_id, :supplier_id, :stock_in_id, :purchase_order_id,
             :original_amount, :paid_amount, :balance_amount, :due_date, :status,
             :payment_terms, :invoice_number, :invoice_date, :invoice_amount,
             :created_by, :notes)
        """)

        payable_params = {
            'payable_number': payable_number,
            'area_id': user_area.id,
            'supplier_id': stock_in.supplier_id,
            'stock_in_id': stock_in_id,
            'purchase_order_id': stock_in.purchase_order_id,
            'original_amount': float(stock_in.total_cost),  # 转换为 float
            'paid_amount': 0.0,  # 使用 float
            'balance_amount': float(stock_in.total_cost),  # 转换为 float
            'due_date': None,
            'status': '未付款',
            'payment_terms': None,
            'invoice_number': None,
            'invoice_date': None,
            'invoice_amount': None,
            'created_by': current_user.id,
            'notes': None
        }

        # 生成财务凭证号
        voucher_prefix = f"PZ{today.strftime('%Y%m%d')}"
        last_voucher = FinancialVoucher.query.filter(
            FinancialVoucher.area_id == user_area.id,
            FinancialVoucher.voucher_number.like(f'{voucher_prefix}%')
        ).order_by(FinancialVoucher.voucher_number.desc()).first()

        if last_voucher:
            last_number = int(last_voucher.voucher_number[-3:])
            voucher_number = f"{voucher_prefix}{last_number + 1:03d}"
        else:
            voucher_number = f"{voucher_prefix}001"

        # 使用原始 SQL 创建财务凭证（遵循 README.md 方案：不包含时间字段）
        sql_voucher = text("""
            INSERT INTO financial_vouchers
            (voucher_number, voucher_date, area_id, voucher_type, summary,
             total_amount, status, source_type, source_id, created_by)
            OUTPUT inserted.id
            VALUES
            (:voucher_number, :voucher_date, :area_id, :voucher_type, :summary,
             :total_amount, :status, :source_type, :source_id, :created_by)
        """)

        voucher_params = {
            'voucher_number': voucher_number,
            'voucher_date': today.strftime('%Y-%m-%d'),  # 转换为字符串格式
            'area_id': user_area.id,
            'voucher_type': '入库凭证',
            'summary': f'入库单{stock_in.stock_in_number}生成应付账款',
            'total_amount': float(stock_in.total_cost),  # 转换为 float
            'status': '已审核',
            'source_type': '入库单',
            'source_id': stock_in_id,
            'created_by': current_user.id
        }
        
        # 获取会计科目
        current_app.logger.info("开始查询会计科目...")

        current_app.logger.info("查询原材料科目 (1201)...")
        inventory_subject = AccountingSubject.query.filter(
            db.or_(
                db.and_(
                    AccountingSubject.area_id == user_area.id,
                    AccountingSubject.code == '1201'
                ),
                db.and_(
                    AccountingSubject.area_id == 1,  # 系统科目
                    AccountingSubject.code == '1201',
                    AccountingSubject.is_system == True
                )
            )
        ).first()

        if inventory_subject:
            current_app.logger.info(f"找到原材料科目: {inventory_subject.name} (ID: {inventory_subject.id})")
        else:
            current_app.logger.error("未找到原材料科目 (1201)")

        current_app.logger.info("查询应付账款科目 (2001)...")
        payable_subject = AccountingSubject.query.filter(
            db.or_(
                db.and_(
                    AccountingSubject.area_id == user_area.id,
                    AccountingSubject.code == '2001'
                ),
                db.and_(
                    AccountingSubject.area_id == 1,  # 系统科目
                    AccountingSubject.code == '2001',
                    AccountingSubject.is_system == True
                )
            )
        ).first()

        if payable_subject:
            current_app.logger.info(f"找到应付账款科目: {payable_subject.name} (ID: {payable_subject.id})")
        else:
            current_app.logger.error("未找到应付账款科目 (2001)")

        if not inventory_subject or not payable_subject:
            current_app.logger.error("会计科目缺失，无法继续")
            return jsonify({
                'success': False,
                'message': '缺少必要的会计科目，请联系管理员初始化系统科目'
            })

        # 准备凭证明细 SQL
        current_app.logger.info("准备凭证明细SQL...")
        sql_detail = text("""
            INSERT INTO voucher_details
            (voucher_id, line_number, subject_id, summary, debit_amount, credit_amount)
            VALUES
            (:voucher_id, :line_number, :subject_id, :summary, :debit_amount, :credit_amount)
        """)

        # 准备更新入库单 SQL
        current_app.logger.info("准备更新入库单SQL...")
        sql_update_stock_in = text("""
            UPDATE stock_ins
            SET payable_id = :payable_id, voucher_id = :voucher_id
            WHERE id = :stock_in_id
        """)

        # 使用单个事务处理所有操作，确保绕过 SQLAlchemy ORM
        current_app.logger.info("开始执行数据库事务...")
        current_app.logger.info(f"应付账款参数: {payable_params}")
        current_app.logger.info(f"财务凭证参数: {voucher_params}")

        with db.engine.begin() as conn:
            current_app.logger.info("事务开始")

            # 1. 创建应付账款
            current_app.logger.info("执行应付账款SQL...")
            result = conn.execute(sql_payable, payable_params)
            payable_id = result.fetchone()[0]
            current_app.logger.info(f"应付账款创建成功，ID: {payable_id}")

            # 2. 创建财务凭证
            current_app.logger.info("执行财务凭证SQL...")
            result = conn.execute(sql_voucher, voucher_params)
            voucher_id = result.fetchone()[0]
            current_app.logger.info(f"财务凭证创建成功，ID: {voucher_id}")

            # 3. 创建凭证明细
            if inventory_subject and payable_subject:
                current_app.logger.info("创建凭证明细...")

                # 借：原材料
                detail1_params = {
                    'voucher_id': voucher_id,
                    'line_number': 1,
                    'subject_id': inventory_subject.id,
                    'summary': f'入库单{stock_in.stock_in_number}',
                    'debit_amount': float(stock_in.total_cost),  # 转换为 float
                    'credit_amount': 0.0
                }
                current_app.logger.info(f"借方明细参数: {detail1_params}")
                conn.execute(sql_detail, detail1_params)
                current_app.logger.info("借方明细创建成功")

                # 贷：应付账款
                detail2_params = {
                    'voucher_id': voucher_id,
                    'line_number': 2,
                    'subject_id': payable_subject.id,
                    'summary': f'入库单{stock_in.stock_in_number}',
                    'debit_amount': 0.0,
                    'credit_amount': float(stock_in.total_cost)  # 转换为 float
                }
                current_app.logger.info(f"贷方明细参数: {detail2_params}")
                conn.execute(sql_detail, detail2_params)
                current_app.logger.info("贷方明细创建成功")
            else:
                current_app.logger.error(f"会计科目缺失: inventory_subject={inventory_subject}, payable_subject={payable_subject}")

            # 4. 更新入库单关联信息
            current_app.logger.info("更新入库单关联信息...")
            update_params = {
                'payable_id': payable_id,
                'voucher_id': voucher_id,
                'stock_in_id': stock_in_id
            }
            current_app.logger.info(f"更新参数: {update_params}")
            conn.execute(sql_update_stock_in, update_params)
            current_app.logger.info("入库单更新成功")

            current_app.logger.info("事务提交成功")
        
        current_app.logger.info(f"=== 应付账款生成成功 ===")
        current_app.logger.info(f"应付账款ID: {payable_id}")
        current_app.logger.info(f"财务凭证ID: {voucher_id}")

        return jsonify({
            'success': True,
            'message': '应付账款生成成功',
            'payable_id': payable_id
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"生成应付账款失败: {str(e)}")
        current_app.logger.error(f"错误类型: {type(e).__name__}")
        current_app.logger.error(f"入库单ID: {stock_in_id}")
        if 'stock_in' in locals():
            current_app.logger.error(f"入库单信息: {stock_in.stock_in_number}, 总金额: {stock_in.total_cost}, 类型: {type(stock_in.total_cost)}")
        return jsonify({'success': False, 'message': f'生成失败: {str(e)}'})


@financial_bp.route('/payables/pending-stock-ins')
@login_required
@school_required
@check_permission('应付账款管理', 'view')
def pending_stock_ins(user_area):
    """待生成应付账款的入库单"""

    # 查询已入库但未生成应付账款的入库单（暂时不要求财务确认）
    stock_ins = StockIn.query.filter(
        StockIn.area_id == user_area.id,
        StockIn.status == '已入库',
        StockIn.payable_id.is_(None)
    ).order_by(StockIn.created_at.desc()).all()

    # 如果没有数据，尝试查询所有已入库的入库单（用于调试）
    if not stock_ins:
        all_stock_ins = StockIn.query.filter(
            StockIn.area_id == user_area.id,
            StockIn.status == '已入库'
        ).all()
        current_app.logger.info(f"该区域所有已入库的入库单数量: {len(all_stock_ins)}")
        for si in all_stock_ins:
            current_app.logger.info(f"入库单: {si.stock_in_number}, payable_id: {si.payable_id}")

    # 添加调试信息
    current_app.logger.info(f"查询到 {len(stock_ins)} 个待生成应付账款的入库单")
    for stock_in in stock_ins:
        current_app.logger.info(f"入库单: {stock_in.stock_in_number}, 状态: {stock_in.status}, 应付账款ID: {stock_in.payable_id}, 总金额: {stock_in.total_cost}")

    # 添加更多调试信息
    total_stock_ins = StockIn.query.filter(StockIn.area_id == user_area.id).count()
    current_app.logger.info(f"该区域总入库单数量: {total_stock_ins}")

    # 检查是否有已入库的入库单
    completed_stock_ins = StockIn.query.filter(
        StockIn.area_id == user_area.id,
        StockIn.status == '已入库'
    ).count()
    current_app.logger.info(f"该区域已入库的入库单数量: {completed_stock_ins}")

    return render_template('financial/payables/pending_stock_ins.html', stock_ins=stock_ins)


@financial_bp.route('/payables/api/summary')
@login_required
@school_required
@check_permission('应付账款管理', 'view')
def payables_summary_api():
    """应付账款汇总API"""
    user_area = current_user.get_current_area()

    # 统计各状态的应付账款
    summary = db.session.query(
        AccountPayable.status,
        db.func.count(AccountPayable.id).label('count'),
        db.func.sum(AccountPayable.balance_amount).label('total_amount')
    ).filter_by(area_id=user_area.id).group_by(AccountPayable.status).all()

    result = {}
    for status, count, total_amount in summary:
        result[status] = {
            'count': count,
            'total_amount': float(total_amount) if total_amount else 0
        }

    return jsonify(result)
