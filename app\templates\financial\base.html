{% extends "base.html" %}

{% block title %}财务管理{% endblock %}

{% block extra_css %}
<style>
    /* 财务模块专用样式 - 基于 yonyou-theme.css */
    @import url('https://fonts.googleapis.com/css2?family=Microsoft+YaHei:wght@300;400;500;600;700&display=swap');

    /* 财务模块全局应用用友样式 */
    .financial-content {
        font-family: var(--uf-font-family);
        font-size: var(--uf-font-size);
        line-height: var(--uf-line-height);
    }

    /* 财务模块特有样式覆盖 */
    .uf-card-header .uf-icon {
        margin-right: 6px;
        color: var(--uf-primary);
    }

    /* 财务模块按钮特殊样式（如有需要可在此添加） */

    /* 财务模块特有样式 - 基于 yonyou-theme.css */

    /* 财务模块全局应用用友样式类 */
    .financial-content {
        font-family: var(--uf-font-family);
        font-size: var(--uf-font-size);
    }

    /* 财务模块表单验证特殊样式 */
    .uf-form-control.uf-error {
        border-color: var(--uf-danger);
        background: #fff5f5;
    }

    .uf-form-control.uf-error:focus {
        box-shadow: 0 0 2px rgba(189, 33, 48, 0.4);
    }

    /* 财务模块特有状态标签 */
    .uf-status-draft {
        background: #fff3cd;
        border-color: #ffeaa7;
        color: #856404;
    }

    .uf-status-posted {
        background: #e2e3e5;
        border-color: #d6d8db;
        color: #383d41;
    }

    /* 财务模块金额特殊样式 */
    .uf-amount-large {
        font-size: 12px;
        font-weight: 600;
    }

    .uf-amount-small {
        font-size: 10px;
    }

    .uf-amount.positive {
        color: #000;
    }

    .uf-amount.negative {
        color: var(--uf-danger);
    }

    .uf-amount.zero {
        color: #666;
    }

    /* 财务模块特有的模块卡片悬停效果 */
    .uf-module-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,102,204,0.2);
        border-color: var(--uf-primary);
    }

    /* 财务模块特有的搜索表单样式 */
    .uf-search-form {
        background: var(--uf-toolbar-bg);
        border: 1px solid var(--uf-border);
        border-radius: 2px;
        padding: 12px;
        margin-bottom: 10px;
    }

    .uf-search-row {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        align-items: end;
    }

    .uf-search-item {
        display: flex;
        flex-direction: column;
        min-width: 120px;
    }

    /* 财务模块统计摘要框 */
    .uf-summary-box {
        background: linear-gradient(135deg, var(--uf-primary) 0%, var(--uf-primary-dark) 100%);
        color: white;
        padding: 16px;
        border-radius: 2px;
        margin-bottom: 10px;
        border: 1px solid var(--uf-primary);
    }

    .uf-summary-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .uf-summary-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 16px;
    }

    .uf-summary-item {
        text-align: center;
    }

    .uf-summary-label {
        font-size: 11px;
        opacity: 0.9;
        margin-bottom: 4px;
    }

    .uf-summary-value {
        font-size: 16px;
        font-weight: 700;
        font-family: 'Courier New', monospace;
    }

    /* 财务模块响应式设计 */
    @media (max-width: 768px) {
        .uf-search-row {
            flex-direction: column;
            align-items: stretch;
        }

        .uf-search-item {
            min-width: auto;
        }

        .uf-summary-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="financial-content">
    <!-- 用友风格面包屑导航 -->
    <nav class="uf-breadcrumb">
        <span class="uf-breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></span>
        <span class="uf-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务管理</a></span>
        {% block breadcrumb %}{% endblock %}
    </nav>

    <!-- 用友风格页面标题栏 -->
    <div class="uf-toolbar">
        <div style="flex: 1;">
            <h2 style="margin: 0; font-size: 14px; font-weight: 600; color: var(--uf-primary);">
                <i class="fas fa-calculator uf-icon"></i>
                {% block page_title %}财务管理{% endblock %}
            </h2>
        </div>
        <div style="display: flex; gap: 8px;">
            {% block page_actions %}{% endblock %}
        </div>
    </div>

    <!-- 页面内容 -->
    {% block financial_content %}{% endblock %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// 用友财务系统风格JavaScript函数

// 用友财务软件专业金额格式化
function ufFormatAmount(amount) {
    if (amount === null || amount === undefined || amount === '') return '0.00';
    const num = parseFloat(amount);
    if (isNaN(num)) return '0.00';
    return num.toFixed(2);
}

// 用友财务软件金额显示（带货币符号和千分位）
function ufFormatAmountDisplay(amount) {
    const formatted = ufFormatAmount(amount);
    const withCommas = formatted.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return '<span class="uf-currency">¥</span>' + withCommas;
}

// 用友财务软件简单金额显示（仅货币符号）
function ufFormatAmountSimple(amount) {
    const formatted = ufFormatAmount(amount);
    return '¥' + formatted;
}

// 用友财务软件金额列对齐格式化
function ufFormatAmountColumn(amount) {
    const formatted = ufFormatAmount(amount);
    const withCommas = formatted.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return '¥' + withCommas.padStart(12, ' ');
}

// 获取用友状态样式类
function ufGetStatusClass(status) {
    const statusMap = {
        '草稿': 'uf-status-draft',
        '待审核': 'uf-status-pending',
        '已审核': 'uf-status-approved',
        '已记账': 'uf-status-posted',
        '未付款': 'uf-status-draft',
        '部分付款': 'uf-status-pending',
        '已付款': 'uf-status-approved'
    };
    return statusMap[status] || 'uf-status-draft';
}

// 用友风格确认对话框
function ufConfirmDelete(message) {
    return confirm(message || '确定要删除这条记录吗？此操作不可撤销。');
}

// 用友风格显示加载状态
function ufShowLoading(element) {
    if (element) {
        element.disabled = true;
        const originalText = element.textContent;
        element.textContent = '处理中...';
        element.dataset.originalText = originalText;
        element.style.cursor = 'wait';
    }
}

// 用友风格隐藏加载状态
function ufHideLoading(element) {
    if (element && element.dataset.originalText) {
        element.disabled = false;
        element.textContent = element.dataset.originalText;
        element.style.cursor = 'pointer';
        delete element.dataset.originalText;
    }
}

// 用友风格消息提示
function ufShowMessage(message, type = 'info') {
    const colors = {
        'success': '#28a745',
        'warning': '#ffc107',
        'danger': '#dc3545',
        'info': '#17a2b8'
    };

    const msgDiv = document.createElement('div');
    msgDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${colors[type] || colors.info};
        color: white;
        padding: 12px 16px;
        border-radius: 2px;
        font-size: 12px;
        z-index: 9999;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        max-width: 300px;
    `;
    msgDiv.textContent = message;

    document.body.appendChild(msgDiv);

    setTimeout(() => {
        msgDiv.remove();
    }, 3000);
}

// 用友财务软件页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化所有金额显示
    document.querySelectorAll('.uf-amount').forEach(function(element) {
        const originalText = element.textContent.trim();
        const amount = parseFloat(originalText.replace(/[¥,]/g, ''));

        if (!isNaN(amount)) {
            element.innerHTML = ufFormatAmountDisplay(amount);

            // 添加正负数样式
            if (amount > 0) {
                element.classList.add('positive');
            } else if (amount < 0) {
                element.classList.add('negative');
            } else {
                element.classList.add('zero');
            }
        }
    });

    // 初始化金额列对齐
    document.querySelectorAll('.uf-amount-col').forEach(function(element) {
        const originalText = element.textContent.trim();
        const amount = parseFloat(originalText.replace(/[¥,]/g, ''));

        if (!isNaN(amount)) {
            element.innerHTML = ufFormatAmountDisplay(amount);
        }
    });

    // 初始化所有状态显示
    document.querySelectorAll('.uf-status').forEach(function(element) {
        const status = element.textContent.trim();
        element.className = 'uf-status ' + ufGetStatusClass(status);
    });

    // 初始化确认删除按钮
    document.querySelectorAll('[data-uf-confirm-delete]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            const message = this.dataset.ufConfirmDelete;
            if (!ufConfirmDelete(message)) {
                e.preventDefault();
                return false;
            }
        });
    });

    // 财务模块特有初始化
    document.body.classList.add('financial-content');
});
</script>
{% block financial_js %}{% endblock %}
{% endblock %}
