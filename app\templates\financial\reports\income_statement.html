{% extends "financial/base.html" %}

{% block title %}成本分析表{% endblock %}

{% block page_title %}成本分析表{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务报表</a></span>
<span class="uf-breadcrumb-item active">成本分析表</span>
{% endblock %}

{% block page_actions %}
<button type="button" class="uf-btn uf-btn-success" onclick="exportCostAnalysis()">
    <i class="fas fa-file-excel uf-icon"></i> 导出Excel
</button>
<button type="button" class="uf-btn uf-btn-info" onclick="printCostAnalysis()">
    <i class="fas fa-print uf-icon"></i> 打印
</button>
<button type="button" class="uf-btn uf-btn-warning" onclick="refreshCostAnalysis()">
    <i class="fas fa-sync uf-icon"></i> 刷新
</button>
{% endblock %}

{% block financial_content %}
<!-- 用友风格成本分析表容器 -->
<div class="uf-cost-analysis-container">
    <!-- 查询条件区域 -->
    <div class="uf-card">
        <div class="uf-card-header">
            <div class="uf-card-header-title">
                <i class="fas fa-calendar uf-card-header-icon"></i>
                分析期间
            </div>
            <div class="uf-card-header-actions">
                <button type="button" class="uf-btn uf-btn-sm" onclick="resetCostQuery()">
                    <i class="fas fa-undo uf-icon"></i> 重置
                </button>
            </div>
        </div>
        <div class="uf-card-body">
            <form method="GET" class="uf-cost-query-form">
                <div class="uf-form-row">
                    <div class="uf-form-group">
                        <label class="uf-form-label">开始日期：</label>
                        <input type="date" class="uf-form-control" id="start_date" name="start_date"
                               value="{{ start_date }}" required>
                    </div>
                    <div class="uf-form-group">
                        <label class="uf-form-label">结束日期：</label>
                        <input type="date" class="uf-form-control" id="end_date" name="end_date"
                               value="{{ end_date }}" required>
                    </div>
                    <div class="uf-form-group">
                        <button type="submit" class="uf-btn uf-btn-primary">
                            <i class="fas fa-chart-line uf-icon"></i> 生成分析
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 成本分析表 -->
    <div class="uf-card">
        <div class="uf-card-header">
            <div class="uf-card-header-title">
                <i class="fas fa-chart-pie uf-card-header-icon"></i>
                成本分析表
            </div>
            <div class="uf-card-header-actions">
                <span class="uf-period-info">
                    分析期间：{{ start_date }} 至 {{ end_date }}
                </span>
            </div>
        </div>
        <div class="uf-card-body" style="padding: 0;">
            <div class="uf-table-container">
                <table class="uf-table uf-cost-analysis-table">
                    <thead>
                        <tr>
                            <th style="width: 200px;">成本项目</th>
                            <th class="uf-amount-col" style="width: 120px;">本期金额</th>
                            <th class="uf-amount-col" style="width: 120px;">上期金额</th>
                            <th class="uf-amount-col" style="width: 120px;">变动金额</th>
                            <th class="uf-amount-col" style="width: 100px;">变动率(%)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 直接成本 -->
                        <tr class="uf-cost-category-row uf-direct-cost-row">
                            <td class="uf-cost-category-name">
                                <i class="fas fa-utensils uf-cost-icon"></i>
                                <strong>一、直接成本</strong>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-bold">{{ "{:,.2f}"|format(cost_analysis.direct_cost_total) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-bold">{{ "{:,.2f}"|format(cost_analysis.direct_cost_total_last) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span>
                                <span class="uf-amount uf-amount-bold {% if cost_analysis.direct_cost_change >= 0 %}uf-amount-increase{% else %}uf-amount-decrease{% endif %}">
                                    {{ "{:,.2f}"|format(cost_analysis.direct_cost_change) }}
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage {% if cost_analysis.direct_cost_change_rate >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(cost_analysis.direct_cost_change_rate) }}%
                                </span>
                            </td>
                        </tr>

                        <!-- 直接成本明细 -->
                        {% for item in cost_analysis.direct_cost_details %}
                        <tr class="uf-cost-detail-row">
                            <td class="uf-cost-detail-name">
                                <span class="uf-cost-indent"></span>
                                <i class="fas fa-circle uf-detail-icon"></i>
                                {{ item.name }}
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount">{{ "{:,.2f}"|format(item.amount) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount">{{ "{:,.2f}"|format(item.amount_last) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span>
                                <span class="uf-amount {% if item.change >= 0 %}uf-amount-increase{% else %}uf-amount-decrease{% endif %}">
                                    {{ "{:,.2f}"|format(item.change) }}
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage {% if item.change_rate >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(item.change_rate) }}%
                                </span>
                            </td>
                        </tr>
                        {% endfor %}

                        <!-- 间接成本 -->
                        <tr class="uf-cost-category-row uf-indirect-cost-row">
                            <td class="uf-cost-category-name">
                                <i class="fas fa-cogs uf-cost-icon"></i>
                                <strong>二、间接成本</strong>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-bold">{{ "{:,.2f}"|format(cost_analysis.indirect_cost_total) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-bold">{{ "{:,.2f}"|format(cost_analysis.indirect_cost_total_last) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span>
                                <span class="uf-amount uf-amount-bold {% if cost_analysis.indirect_cost_change >= 0 %}uf-amount-increase{% else %}uf-amount-decrease{% endif %}">
                                    {{ "{:,.2f}"|format(cost_analysis.indirect_cost_change) }}
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage {% if cost_analysis.indirect_cost_change_rate >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(cost_analysis.indirect_cost_change_rate) }}%
                                </span>
                            </td>
                        </tr>

                        <!-- 间接成本明细 -->
                        {% for item in cost_analysis.indirect_cost_details %}
                        <tr class="uf-cost-detail-row">
                            <td class="uf-cost-detail-name">
                                <span class="uf-cost-indent"></span>
                                <i class="fas fa-circle uf-detail-icon"></i>
                                {{ item.name }}
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount">{{ "{:,.2f}"|format(item.amount) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount">{{ "{:,.2f}"|format(item.amount_last) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span>
                                <span class="uf-amount {% if item.change >= 0 %}uf-amount-increase{% else %}uf-amount-decrease{% endif %}">
                                    {{ "{:,.2f}"|format(item.change) }}
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage {% if item.change_rate >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(item.change_rate) }}%
                                </span>
                            </td>
                        </tr>
                        {% endfor %}

                        <!-- 总成本 -->
                        <tr class="uf-cost-total-row">
                            <td class="uf-cost-category-name">
                                <i class="fas fa-calculator uf-cost-icon"></i>
                                <strong>三、总成本（一+二）</strong>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-total">{{ "{:,.2f}"|format(cost_analysis.total_cost) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-total">{{ "{:,.2f}"|format(cost_analysis.total_cost_last) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span>
                                <span class="uf-amount uf-amount-total {% if cost_analysis.total_cost_change >= 0 %}uf-amount-increase{% else %}uf-amount-decrease{% endif %}">
                                    {{ "{:,.2f}"|format(cost_analysis.total_cost_change) }}
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage uf-percentage-total {% if cost_analysis.total_cost_change_rate >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(cost_analysis.total_cost_change_rate) }}%
                                </span>
                            </td>
                        </tr>

                        <!-- 成本分析指标 -->
                        <tr class="uf-indicator-header-row">
                            <td colspan="5" class="uf-indicator-header">
                                <i class="fas fa-chart-bar uf-cost-icon"></i>
                                <strong>成本分析指标</strong>
                            </td>
                        </tr>
                        <tr class="uf-indicator-row">
                            <td class="uf-indicator-name">
                                <span class="uf-cost-indent"></span>
                                <i class="fas fa-user uf-detail-icon"></i>
                                单位用餐成本（元/人次）
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-indicator">{{ "{:.2f}"|format(cost_analysis.cost_per_meal) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-indicator">{{ "{:.2f}"|format(cost_analysis.cost_per_meal_last) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span>
                                <span class="uf-amount uf-amount-indicator {% if cost_analysis.cost_per_meal_change >= 0 %}uf-amount-increase{% else %}uf-amount-decrease{% endif %}">
                                    {{ "{:.2f}"|format(cost_analysis.cost_per_meal_change) }}
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage {% if cost_analysis.cost_per_meal_change_rate >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(cost_analysis.cost_per_meal_change_rate) }}%
                                </span>
                            </td>
                        </tr>
                        <tr class="uf-indicator-row">
                            <td class="uf-indicator-name">
                                <span class="uf-cost-indent"></span>
                                <i class="fas fa-percentage uf-detail-icon"></i>
                                直接成本占比
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage uf-percentage-indicator">{{ "{:.1f}"|format(cost_analysis.direct_cost_ratio) }}%</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage uf-percentage-indicator">{{ "{:.1f}"|format(cost_analysis.direct_cost_ratio_last) }}%</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage uf-percentage-indicator {% if cost_analysis.direct_cost_ratio_change >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(cost_analysis.direct_cost_ratio_change) }}%
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-amount-zero">-</span>
                            </td>
                        </tr>
                        <tr class="uf-indicator-row">
                            <td class="uf-indicator-name">
                                <span class="uf-cost-indent"></span>
                                <i class="fas fa-percentage uf-detail-icon"></i>
                                间接成本占比
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage uf-percentage-indicator">{{ "{:.1f}"|format(cost_analysis.indirect_cost_ratio) }}%</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage uf-percentage-indicator">{{ "{:.1f}"|format(cost_analysis.indirect_cost_ratio_last) }}%</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage uf-percentage-indicator {% if cost_analysis.indirect_cost_ratio_change >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(cost_analysis.indirect_cost_ratio_change) }}%
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-amount-zero">-</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

                    <!-- 报表说明 -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> 报表说明</h6>
                                <ul class="mb-0">
                                    <li>分析期间：{{ start_date }} 至 {{ end_date }}</li>
                                    <li>编制单位：{{ user_area.name }}</li>
                                    <li>金额单位：人民币元</li>
                                    <li>上期金额为同期对比数据</li>
                                    <li>直接成本：食材采购、加工等直接用于餐饮的成本</li>
                                    <li>间接成本：人工、水电、设备折旧等运营成本</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const url = `{{ url_for('financial.export_report', report_type='cost_analysis') }}?start_date=${startDate}&end_date=${endDate}`;
    window.open(url, '_blank');
}
</script>
{% endblock %}
