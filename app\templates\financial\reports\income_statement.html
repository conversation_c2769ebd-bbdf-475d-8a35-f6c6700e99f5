{% extends "financial/base.html" %}

{% block title %}成本分析表{% endblock %}

{% block page_title %}成本分析表{% endblock %}

{% block breadcrumb %}
<span class="uf-breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务报表</a></span>
<span class="uf-breadcrumb-item active">成本分析表</span>
{% endblock %}

{% block page_actions %}
<button type="button" class="uf-btn uf-btn-success" onclick="exportCostAnalysis()">
    <i class="fas fa-file-excel uf-icon"></i> 导出Excel
</button>
<button type="button" class="uf-btn uf-btn-info" onclick="printCostAnalysis()">
    <i class="fas fa-print uf-icon"></i> 打印
</button>
<button type="button" class="uf-btn uf-btn-warning" onclick="refreshCostAnalysis()">
    <i class="fas fa-sync uf-icon"></i> 刷新
</button>
{% endblock %}

{% block financial_content %}
<!-- 用友风格成本分析表容器 -->
<div class="uf-cost-analysis-container">
    <!-- 查询条件区域 -->
    <div class="uf-card">
        <div class="uf-card-header">
            <div class="uf-card-header-title">
                <i class="fas fa-calendar uf-card-header-icon"></i>
                分析期间
            </div>
            <div class="uf-card-header-actions">
                <button type="button" class="uf-btn uf-btn-sm" onclick="resetCostQuery()">
                    <i class="fas fa-undo uf-icon"></i> 重置
                </button>
            </div>
        </div>
        <div class="uf-card-body">
            <form method="GET" class="uf-cost-query-form">
                <div class="uf-form-row">
                    <div class="uf-form-group">
                        <label class="uf-form-label">开始日期：</label>
                        <input type="date" class="uf-form-control" id="start_date" name="start_date"
                               value="{{ start_date }}" required>
                    </div>
                    <div class="uf-form-group">
                        <label class="uf-form-label">结束日期：</label>
                        <input type="date" class="uf-form-control" id="end_date" name="end_date"
                               value="{{ end_date }}" required>
                    </div>
                    <div class="uf-form-group">
                        <button type="submit" class="uf-btn uf-btn-primary">
                            <i class="fas fa-chart-line uf-icon"></i> 生成分析
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 成本分析表 -->
    <div class="uf-card">
        <div class="uf-card-header">
            <div class="uf-card-header-title">
                <i class="fas fa-chart-pie uf-card-header-icon"></i>
                成本分析表
            </div>
            <div class="uf-card-header-actions">
                <span class="uf-period-info">
                    分析期间：{{ start_date }} 至 {{ end_date }}
                </span>
            </div>
        </div>
        <div class="uf-card-body" style="padding: 0;">
            <div class="uf-table-container">
                <table class="uf-table uf-cost-analysis-table">
                    <thead>
                        <tr>
                            <th style="width: 200px;">成本项目</th>
                            <th class="uf-amount-col" style="width: 120px;">本期金额</th>
                            <th class="uf-amount-col" style="width: 120px;">上期金额</th>
                            <th class="uf-amount-col" style="width: 120px;">变动金额</th>
                            <th class="uf-amount-col" style="width: 100px;">变动率(%)</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 直接成本 -->
                        <tr class="uf-cost-category-row uf-direct-cost-row">
                            <td class="uf-cost-category-name">
                                <i class="fas fa-utensils uf-cost-icon"></i>
                                <strong>一、直接成本</strong>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-bold">{{ "{:,.2f}"|format(cost_analysis.direct_cost_total) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-bold">{{ "{:,.2f}"|format(cost_analysis.direct_cost_total_last) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span>
                                <span class="uf-amount uf-amount-bold {% if cost_analysis.direct_cost_change >= 0 %}uf-amount-increase{% else %}uf-amount-decrease{% endif %}">
                                    {{ "{:,.2f}"|format(cost_analysis.direct_cost_change) }}
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage {% if cost_analysis.direct_cost_change_rate >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(cost_analysis.direct_cost_change_rate) }}%
                                </span>
                            </td>
                        </tr>

                        <!-- 直接成本明细 -->
                        {% for item in cost_analysis.direct_cost_details %}
                        <tr class="uf-cost-detail-row">
                            <td class="uf-cost-detail-name">
                                <span class="uf-cost-indent"></span>
                                <i class="fas fa-circle uf-detail-icon"></i>
                                {{ item.name }}
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount">{{ "{:,.2f}"|format(item.amount) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount">{{ "{:,.2f}"|format(item.amount_last) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span>
                                <span class="uf-amount {% if item.change >= 0 %}uf-amount-increase{% else %}uf-amount-decrease{% endif %}">
                                    {{ "{:,.2f}"|format(item.change) }}
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage {% if item.change_rate >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(item.change_rate) }}%
                                </span>
                            </td>
                        </tr>
                        {% endfor %}

                        <!-- 间接成本 -->
                        <tr class="uf-cost-category-row uf-indirect-cost-row">
                            <td class="uf-cost-category-name">
                                <i class="fas fa-cogs uf-cost-icon"></i>
                                <strong>二、间接成本</strong>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-bold">{{ "{:,.2f}"|format(cost_analysis.indirect_cost_total) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-bold">{{ "{:,.2f}"|format(cost_analysis.indirect_cost_total_last) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span>
                                <span class="uf-amount uf-amount-bold {% if cost_analysis.indirect_cost_change >= 0 %}uf-amount-increase{% else %}uf-amount-decrease{% endif %}">
                                    {{ "{:,.2f}"|format(cost_analysis.indirect_cost_change) }}
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage {% if cost_analysis.indirect_cost_change_rate >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(cost_analysis.indirect_cost_change_rate) }}%
                                </span>
                            </td>
                        </tr>

                        <!-- 间接成本明细 -->
                        {% for item in cost_analysis.indirect_cost_details %}
                        <tr class="uf-cost-detail-row">
                            <td class="uf-cost-detail-name">
                                <span class="uf-cost-indent"></span>
                                <i class="fas fa-circle uf-detail-icon"></i>
                                {{ item.name }}
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount">{{ "{:,.2f}"|format(item.amount) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount">{{ "{:,.2f}"|format(item.amount_last) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span>
                                <span class="uf-amount {% if item.change >= 0 %}uf-amount-increase{% else %}uf-amount-decrease{% endif %}">
                                    {{ "{:,.2f}"|format(item.change) }}
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage {% if item.change_rate >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(item.change_rate) }}%
                                </span>
                            </td>
                        </tr>
                        {% endfor %}

                        <!-- 总成本 -->
                        <tr class="uf-cost-total-row">
                            <td class="uf-cost-category-name">
                                <i class="fas fa-calculator uf-cost-icon"></i>
                                <strong>三、总成本（一+二）</strong>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-total">{{ "{:,.2f}"|format(cost_analysis.total_cost) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-total">{{ "{:,.2f}"|format(cost_analysis.total_cost_last) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span>
                                <span class="uf-amount uf-amount-total {% if cost_analysis.total_cost_change >= 0 %}uf-amount-increase{% else %}uf-amount-decrease{% endif %}">
                                    {{ "{:,.2f}"|format(cost_analysis.total_cost_change) }}
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage uf-percentage-total {% if cost_analysis.total_cost_change_rate >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(cost_analysis.total_cost_change_rate) }}%
                                </span>
                            </td>
                        </tr>

                        <!-- 成本分析指标 -->
                        <tr class="uf-indicator-header-row">
                            <td colspan="5" class="uf-indicator-header">
                                <i class="fas fa-chart-bar uf-cost-icon"></i>
                                <strong>成本分析指标</strong>
                            </td>
                        </tr>
                        <tr class="uf-indicator-row">
                            <td class="uf-indicator-name">
                                <span class="uf-cost-indent"></span>
                                <i class="fas fa-user uf-detail-icon"></i>
                                单位用餐成本（元/人次）
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-indicator">{{ "{:.2f}"|format(cost_analysis.cost_per_meal) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span><span class="uf-amount uf-amount-indicator">{{ "{:.2f}"|format(cost_analysis.cost_per_meal_last) }}</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-currency">¥</span>
                                <span class="uf-amount uf-amount-indicator {% if cost_analysis.cost_per_meal_change >= 0 %}uf-amount-increase{% else %}uf-amount-decrease{% endif %}">
                                    {{ "{:.2f}"|format(cost_analysis.cost_per_meal_change) }}
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage {% if cost_analysis.cost_per_meal_change_rate >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(cost_analysis.cost_per_meal_change_rate) }}%
                                </span>
                            </td>
                        </tr>
                        <tr class="uf-indicator-row">
                            <td class="uf-indicator-name">
                                <span class="uf-cost-indent"></span>
                                <i class="fas fa-percentage uf-detail-icon"></i>
                                直接成本占比
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage uf-percentage-indicator">{{ "{:.1f}"|format(cost_analysis.direct_cost_ratio) }}%</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage uf-percentage-indicator">{{ "{:.1f}"|format(cost_analysis.direct_cost_ratio_last) }}%</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage uf-percentage-indicator {% if cost_analysis.direct_cost_ratio_change >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(cost_analysis.direct_cost_ratio_change) }}%
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-amount-zero">-</span>
                            </td>
                        </tr>
                        <tr class="uf-indicator-row">
                            <td class="uf-indicator-name">
                                <span class="uf-cost-indent"></span>
                                <i class="fas fa-percentage uf-detail-icon"></i>
                                间接成本占比
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage uf-percentage-indicator">{{ "{:.1f}"|format(cost_analysis.indirect_cost_ratio) }}%</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage uf-percentage-indicator">{{ "{:.1f}"|format(cost_analysis.indirect_cost_ratio_last) }}%</span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-percentage uf-percentage-indicator {% if cost_analysis.indirect_cost_ratio_change >= 0 %}uf-percentage-increase{% else %}uf-percentage-decrease{% endif %}">
                                    {{ "{:.1f}"|format(cost_analysis.indirect_cost_ratio_change) }}%
                                </span>
                            </td>
                            <td class="uf-amount-col">
                                <span class="uf-amount-zero">-</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 报表说明 -->
    <div class="uf-card uf-info-card">
        <div class="uf-card-header">
            <div class="uf-card-header-title">
                <i class="fas fa-info-circle uf-card-header-icon"></i>
                报表说明
            </div>
        </div>
        <div class="uf-card-body">
            <div class="uf-info-list">
                <div class="uf-info-item">
                    <i class="fas fa-calendar uf-info-icon"></i>
                    <span>分析期间：{{ start_date }} 至 {{ end_date }}</span>
                </div>
                <div class="uf-info-item">
                    <i class="fas fa-building uf-info-icon"></i>
                    <span>编制单位：{{ user_area.name }}</span>
                </div>
                <div class="uf-info-item">
                    <i class="fas fa-money-bill uf-info-icon"></i>
                    <span>金额单位：人民币元</span>
                </div>
                <div class="uf-info-item">
                    <i class="fas fa-clock uf-info-icon"></i>
                    <span>上期金额为同期对比数据</span>
                </div>
                <div class="uf-info-item">
                    <i class="fas fa-utensils uf-info-icon"></i>
                    <span>直接成本：食材采购、加工等直接用于餐饮的成本</span>
                </div>
                <div class="uf-info-item">
                    <i class="fas fa-cogs uf-info-icon"></i>
                    <span>间接成本：人工、水电、设备折旧等运营成本</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
// 用友风格成本分析表页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initUFCostAnalysis();
});

function initUFCostAnalysis() {
    // 初始化表格排序
    initCostTableSorting();

    // 初始化快捷键
    initCostKeyboardShortcuts();

    // 初始化工具提示
    initCostTooltips();

    // 初始化成本分析图表（可选）
    initCostCharts();
}

function initCostTableSorting() {
    // 为表头添加排序功能
    const headers = document.querySelectorAll('.uf-cost-analysis-table th');
    headers.forEach((header, index) => {
        if (index > 0 && index < 5) { // 金额列可排序
            header.classList.add('sortable');
            header.addEventListener('click', () => sortCostTable(index));
        }
    });
}

function sortCostTable(columnIndex) {
    const table = document.querySelector('.uf-cost-analysis-table');
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr:not(.uf-indicator-header-row)'));

    if (rows.length === 0) return;

    // 获取当前排序状态
    const header = table.querySelectorAll('th')[columnIndex];
    const isAsc = !header.classList.contains('sort-asc');

    // 清除所有排序标记
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });

    // 设置当前排序标记
    header.classList.add(isAsc ? 'sort-asc' : 'sort-desc');

    // 排序行（只排序成本项目行，不排序指标行）
    const costRows = rows.filter(row =>
        !row.classList.contains('uf-indicator-row') &&
        !row.classList.contains('uf-indicator-header-row')
    );

    costRows.sort((a, b) => {
        const aCellText = a.cells[columnIndex].textContent.trim();
        const bCellText = b.cells[columnIndex].textContent.trim();
        const aValue = parseFloat(aCellText.replace(/[¥,%\-]/g, '')) || 0;
        const bValue = parseFloat(bCellText.replace(/[¥,%\-]/g, '')) || 0;
        return isAsc ? aValue - bValue : bValue - aValue;
    });

    // 重新插入排序后的行
    costRows.forEach(row => tbody.appendChild(row));
}

function initCostKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+E: 导出
        if (e.ctrlKey && e.key === 'e') {
            e.preventDefault();
            exportCostAnalysis();
        }

        // Ctrl+P: 打印
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            printCostAnalysis();
        }

        // Ctrl+R: 刷新
        if (e.ctrlKey && e.key === 'r') {
            e.preventDefault();
            refreshCostAnalysis();
        }

        // F5: 刷新查询
        if (e.key === 'F5') {
            e.preventDefault();
            document.querySelector('.uf-cost-query-form').submit();
        }
    });
}

function initCostTooltips() {
    // 为金额单元格添加工具提示
    const amountCells = document.querySelectorAll('.uf-amount');
    amountCells.forEach(cell => {
        const value = cell.textContent.replace(/[¥,]/g, '');
        if (value && value !== '-') {
            cell.title = `金额：${value}`;
        }
    });

    // 为百分比单元格添加工具提示
    const percentageCells = document.querySelectorAll('.uf-percentage');
    percentageCells.forEach(cell => {
        const value = cell.textContent.replace(/%/g, '');
        if (value && value !== '-') {
            cell.title = `比率：${value}%`;
        }
    });
}

function initCostCharts() {
    // 这里可以添加图表初始化代码
    // 例如使用 Chart.js 或 ECharts 创建成本构成饼图
}

function exportCostAnalysis() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;

    if (!startDate || !endDate) {
        alert('请选择分析期间');
        return;
    }

    const url = `{{ url_for('financial.export_report', report_type='cost_analysis') }}?start_date=${startDate}&end_date=${endDate}`;
    window.open(url, '_blank');
}

function printCostAnalysis() {
    // 创建打印窗口
    const printWindow = window.open('', '_blank');
    const printContent = generateCostPrintContent();

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
}

function generateCostPrintContent() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;

    const table = document.querySelector('.uf-cost-analysis-table').outerHTML;

    return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>成本分析表</title>
            <style>
                body { font-family: 'Microsoft YaHei', sans-serif; font-size: 12px; }
                .print-header { text-align: center; margin-bottom: 20px; }
                .print-info { margin-bottom: 10px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #999; padding: 4px 6px; text-align: center; }
                th { background: #f0f8ff; font-weight: 600; }
                .uf-amount-col { text-align: right; }
                .uf-cost-indent { display: none; }
                .uf-cost-icon, .uf-detail-icon { display: none; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            <div class="print-header">
                <h2>成本分析表</h2>
                <div class="print-info">分析期间：${startDate} 至 ${endDate}</div>
                <div class="print-info">编制单位：{{ user_area.name }}</div>
                <div class="print-info">打印时间：${new Date().toLocaleString()}</div>
            </div>
            ${table}
        </body>
        </html>
    `;
}

function refreshCostAnalysis() {
    window.location.reload();
}

function resetCostQuery() {
    const today = new Date().toISOString().split('T')[0];
    const firstDay = new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];

    document.getElementById('start_date').value = firstDay;
    document.getElementById('end_date').value = today;
}
</script>
{% endblock %}

{% block financial_css %}
<style>
/* 用友风格成本分析表页面专用样式 */
.uf-cost-analysis-container {
    background: var(--uf-light);
    min-height: 100vh;
    padding: 0;
}

/* 查询表单样式 */
.uf-cost-query-form {
    margin: 0;
}

/* 期间信息样式 */
.uf-period-info {
    font-size: 11px;
    color: var(--uf-muted);
    font-weight: normal;
}

/* 成本分析表格专用样式 */
.uf-cost-analysis-table {
    font-size: 11px;
    width: 100%;
}

.uf-cost-analysis-table th {
    background: linear-gradient(to bottom, #e6f2ff 0%, #cce5ff 100%);
    color: var(--uf-primary);
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    position: relative;
    padding: 8px 6px;
    border: 1px solid #999;
}

.uf-cost-analysis-table th.sortable {
    cursor: pointer;
    user-select: none;
}

.uf-cost-analysis-table th.sortable:hover {
    background: linear-gradient(to bottom, #d9e8ff 0%, #b3d9ff 100%);
}

.uf-cost-analysis-table th.sort-asc::after {
    content: "↑";
    position: absolute;
    right: 4px;
    font-size: 10px;
    color: var(--uf-primary);
}

.uf-cost-analysis-table th.sort-desc::after {
    content: "↓";
    position: absolute;
    right: 4px;
    font-size: 10px;
    color: var(--uf-primary);
}

.uf-cost-analysis-table td {
    padding: 6px;
    vertical-align: middle;
    border: 1px solid #999;
}

.uf-cost-analysis-table tbody tr:hover {
    background: var(--uf-row-hover);
}

/* 成本分类行样式 - 根据您提供的效果图 */
.uf-cost-category-row {
    font-weight: 600;
}

.uf-direct-cost-row {
    background: linear-gradient(to right, #b8e6b8 0%, #d4f0d4 100%); /* 浅绿色渐变 */
}

.uf-indirect-cost-row {
    background: linear-gradient(to right, #fff2cc 0%, #fff8e1 100%); /* 浅黄色渐变 */
}

.uf-cost-total-row {
    background: linear-gradient(to right, #b8e6b8 0%, #d4f0d4 100%); /* 浅绿色渐变 */
    font-weight: 700;
}

/* 成本项目名称样式 */
.uf-cost-category-name {
    text-align: left;
    padding-left: 8px;
}

.uf-cost-detail-name {
    text-align: left;
    padding-left: 8px;
}

.uf-indicator-name {
    text-align: left;
    padding-left: 8px;
}

/* 成本缩进样式 */
.uf-cost-indent {
    display: inline-block;
    width: 16px;
}

/* 成本图标样式 */
.uf-cost-icon {
    margin-right: 6px;
    color: var(--uf-primary);
    font-size: 12px;
}

.uf-detail-icon {
    margin-right: 4px;
    color: var(--uf-muted);
    font-size: 8px;
}

/* 金额变动样式 */
.uf-amount-increase {
    color: var(--uf-danger); /* 红色表示增加 */
}

.uf-amount-decrease {
    color: var(--uf-success); /* 绿色表示减少 */
}

/* 百分比样式 */
.uf-percentage {
    font-weight: 500;
}

.uf-percentage-increase {
    color: var(--uf-danger); /* 红色表示增长 */
}

.uf-percentage-decrease {
    color: var(--uf-success); /* 绿色表示下降 */
}

.uf-percentage-total {
    font-weight: 700;
    color: var(--uf-primary);
}

.uf-percentage-indicator {
    color: var(--uf-info);
    font-weight: 500;
}

/* 金额指标样式 */
.uf-amount-indicator {
    color: var(--uf-info);
    font-weight: 500;
}

/* 指标行样式 */
.uf-indicator-header-row {
    background: linear-gradient(to right, #f0f8ff 0%, #e6f2ff 100%);
}

.uf-indicator-header {
    text-align: left;
    padding-left: 8px;
    font-weight: 600;
    color: var(--uf-primary);
}

.uf-indicator-row {
    background: #fafbfc;
}

/* 分析网格布局 */
.uf-analysis-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

/* 成本构成分析样式 */
.uf-cost-composition {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.uf-composition-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
}

.uf-composition-total {
    background: #e8f5e8;
    border-color: var(--uf-success);
    font-weight: 600;
}

.uf-composition-label {
    font-weight: 500;
    color: #333;
    font-size: 11px;
}

.uf-composition-value {
    font-weight: 600;
    font-size: 12px;
    color: var(--uf-primary);
}

.uf-composition-ratio {
    font-size: 10px;
    color: var(--uf-muted);
    margin-left: 4px;
}

/* 趋势分析样式 */
.uf-trend-analysis {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.uf-trend-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
}

.uf-trend-label {
    font-weight: 500;
    color: #333;
    font-size: 11px;
}

.uf-trend-value {
    font-weight: 600;
    font-size: 12px;
}

.uf-trend-rate {
    font-size: 10px;
    margin-left: 4px;
}

.uf-trend-up {
    color: var(--uf-danger);
}

.uf-trend-down {
    color: var(--uf-success);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .uf-analysis-grid {
        grid-template-columns: 1fr;
    }

    .uf-cost-analysis-table {
        font-size: 10px;
    }

    .uf-cost-analysis-table th,
    .uf-cost-analysis-table td {
        padding: 4px;
    }

    .uf-composition-item,
    .uf-trend-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}

/* 打印样式 */
@media print {
    .uf-card-header,
    .uf-btn,
    .page-actions,
    .uf-analysis-grid,
    .uf-info-card {
        display: none !important;
    }

    .uf-card {
        border: none;
        box-shadow: none;
        margin: 0;
    }

    .uf-card-body {
        padding: 0;
    }

    .uf-cost-analysis-table {
        font-size: 10px;
    }

    .uf-cost-analysis-table th,
    .uf-cost-analysis-table td {
        padding: 3px;
    }

    .uf-cost-indent {
        display: none;
    }

    .uf-cost-icon,
    .uf-detail-icon {
        display: none;
    }

    /* 保持打印时的颜色效果 */
    .uf-direct-cost-row {
        background: #b8e6b8 !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .uf-indirect-cost-row {
        background: #fff2cc !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .uf-cost-total-row {
        background: #b8e6b8 !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .uf-indicator-header-row {
        background: #f0f8ff !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .uf-indicator-row {
        background: #fafbfc !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }
}
</style>
{% endblock %}
